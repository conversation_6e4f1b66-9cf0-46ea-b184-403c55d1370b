/* Base Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.footerContainer{
    position: relative;
}

body {
    font-family: 'Segoe UI', sans-serif;
    background: white;
    color: #ffffff;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    padding-bottom: 80px; /* Add space for footer */
}

/* Header */
header {
    padding: 20px;
    text-align: center;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    font-size: 1.8rem;
    font-weight: bold;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.5); */
    width: 100%;
    position: fixed;
    top:0;
    z-index: 1000;
}

main.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: whitesmoke;
    border-radius: 10px;
    margin: 180px auto 40px auto; /* Top margin for fixed header/nav, bottom margin for spacing */
    margin-left: 270px; /* Account for sidebar width */
    width: calc(100% - 320px); /* Adjust width to account for sidebar */
    max-width: 800px;
    min-height: calc(100vh - 300px); /* Ensure minimum height */
    box-shadow: 0 2px 6px rgba(0,0,0,0.5);
    color: black;
}

/* Navigation Bar */
nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    padding: 15px 30px;
    position: fixed;
    top: 90px;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.4); */
    width: 100%;
    z-index: 1000;
}

nav .logo {
    font-size: 1.5rem;
    font-weight: bold;
    width: auto;
    height: auto;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 25px;
}

nav ul li a {
    color: #fff;
    text-decoration: none;
    padding: 8px 14px;
    /* transition: background 0.3s ease; */
    border-radius: 6px;
}

nav ul li a:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

/* Sidebar */
aside {
    background: linear-gradient(135deg, #2c3e50, #4ca1af);
    padding: 20px;
    width: 250px;
    height: calc(100vh - 150px); /* Full height minus header/nav */
    position: fixed;
    top: 150px;
    left: 0;
    overflow-y: auto;
    z-index: 999;
}

aside h2 {
    font-size: 1.4rem;
    margin-bottom: 20px;
    color: #f5f5f5;
}

aside ul {
    list-style-type: none;
}

aside ul li {
    margin-bottom: 15px;
}

aside ul li a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

aside ul li a:hover {
    color: #00adb5;
}

/* For responsive layout */
@media (max-width: 768px) {
    nav {
        flex-direction: column;
        padding: 10px;
        top: 70px; /* Adjust for smaller header */
    }

    nav ul {
        flex-direction: column;
        gap: 10px;
        margin-top: 10px;
    }

    header {
        font-size: 1.4rem;
        padding: 15px;
    }

    aside {
        width: 100%;
        position: relative;
        height: auto;
        top: 0;
        left: 0;
        margin-bottom: 20px;
    }

    main.container {
        margin: 200px 20px 40px 20px;
        width: calc(100% - 40px);
        max-width: none;
    }

    .expense-list {
        width: 100%;
        max-width: none;
    }
}
.btn {
    background:linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-radius: 5px;
    margin-top: 1rem;
}
.btn:hover {
    background-color: rgba(0, 0, 0, 0.25);
}
.btn-cancel {
    background-color: #3498db;
    text-decoration: none;
    padding: 0.5rem 1rem;
    color: white;
    border-radius: 5px;
    display: inline-block;
    margin-top: 1rem;
}
.btn-cancel:hover {
    background-color: #2980b9;
}
.expense-list {
    margin-top: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    color: #333;
    width: 700px;
    height: auto;
}
.expense-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #ddd;
    color: #333;
}
.expense-item:last-child {
    border-bottom: none;
}
.btn-delete, .btn-edit {
    color: white;
    text-decoration: none;
    padding: 0.3rem 0.6rem;
    border-radius: 3px;
}
.btn-delete {
    background-color: #e74c3c;
}
.btn-edit {
    background-color: #3498db;
}
.btn-delete:hover {
    background-color: #c0392b;
}
.btn-edit:hover {
    background-color: #2980b9;
}
canvas {
    margin-top: 2rem;
    background: white;
    padding: 1rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
/* Footer */
footer {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    margin-top: 2rem;
    width: 100%;
    position: relative;
    clear: both;
    margin-left: 0;
}
.error {
    color: red;
    margin-bottom: 1rem;
}

/* Modal and Form Styles */
.modal {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    margin: 2rem 0;
    color: #333;
}

.modal-content {
    width: 100%;
}

.modal h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.modal label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #333;
}

.modal input {
    width: 100%;
    padding: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.card {
    background: white;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin: 1rem 0;
}