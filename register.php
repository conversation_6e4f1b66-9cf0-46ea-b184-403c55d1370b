<?php
session_start();
require 'db.php'; // Include your database connection

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = trim($_POST['name']);
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
    $role = $_POST['role'] === 'admin' ? 'admin' : 'user';

    try {
        // Check if email already exists
        $checkStmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $checkStmt->execute([$email]);
        
        if ($checkStmt->rowCount() > 0) {
            echo "<script>alert('Email already registered!');</script>";
        } else {
            // Insert new user
            $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
            if ($stmt->execute([$name, $email, $password, $role])) {
                $_SESSION['user_id'] = $pdo->lastInsertId();
                $_SESSION['role'] = $role;
                $_SESSION['name'] = $name;
                header('Location: perosnal/personal_dashboard.php');
                exit();
            } else {
                echo "<script>alert('Registration failed. Please try again.');</script>";
            }
        }
    } catch (PDOException $e) {
        echo "<script>alert('An error occurred. Please try again later.');</script>";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h1>Create Your Account</h1>
        <p>Start tracking your expenses today</p>
    </header>

    <main>
        <form id="login-form" class="form-container" method="post" action="register.php">
            <div class="form-group">
                <input type="text" id="name" name="name" placeholder="Name" required>
            </div>
            <div class="form-group">
                <input type="email" id="email" name="email" placeholder="Email" required>
            </div>
            <div class="form-group">
                <input type="password" id="password" name="password" placeholder="Password" required>
            </div><CENTER>
            <div class="form-group">
                <select id="role" name="role" required>
                    <option value="" disabled selected>Select Role</option>
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                </select>
            </div><br>
            <button type="submit" class="btn">Register</button>
            <p class="text-center">Already have an account? <a href="login.php">Login here</a></p>
        </form></CENTER>
    </main>

    <footer>
        <p>&copy; 2023 Expense Tracker. All Rights Reserved.</p>
    </footer>
</body>
</html>