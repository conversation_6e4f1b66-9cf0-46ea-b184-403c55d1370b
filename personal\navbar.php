<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require '../db.php'; // Include the database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: ../login.php'); // Redirect to login if not logged in
    exit();
}

$user_id = $_SESSION['user_id'];
?>

<header>
    <h2>Personal Expense Tracker</h2>
</header>
<nav>
    <div class="logo" style="display: inline-block;">
        <img src="../spendify.jpg" width="50" height="50">Expense Tracker
    </div>
    <ul>
        <li><a href="personal_dashboard.php">Personal Expense</a></li>
        <li><a href="../group/group_dashboard.php">Group Expense</a></li>
        <li><a href="../aboutus.php">About Us</a></li>
        <li><a href="../contact.php">Contact Us</a></li>
    </ul>
</nav>
<aside>
    <ul>
        <li><a href="addexpense.php">Add Expense</a></li>
        <li><a href="view_expenses.php">View Expenses</a></li>
        <li><a href="reports.php">Reports</a></li>
        <hr>
        <li><a href="../logout.php">Logout</a></li>
    </ul>
</aside>