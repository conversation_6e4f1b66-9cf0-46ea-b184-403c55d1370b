<?php
session_start();
require '../db.php'; // Include the database connection

if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'user') {
    header('Location: login.php'); // Redirect to login if not logged in
    exit();
}

$user_id = $_SESSION['user_id'];

// Retrieve expenses for the logged-in user
$stmt = $pdo->prepare("SELECT * FROM expenses WHERE user_id = ?");
$stmt->execute([$user_id]);
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetching expenses for charting
$stmt = $pdo->prepare("SELECT name, amount, DATE_FORMAT(created_at, '%Y-%m-%d') as date FROM expenses WHERE user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$chartExpenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Prepare data for the chart
$labels = [];
$data = [];
foreach ($chartExpenses as $expense) {
    $labels[] = $expense['date'];
    $data[] = $expense['amount'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
<?php include 'navbar.php'; // Include the header file ?>

<main class="container" align="center">
    <div class="card">
        <h2>Your Expenses Report</h2>
        <div class="modal-content">
            <canvas id="expensesChart" style="color: black;"></canvas>
        </div>
    </div>
</main>
<script>
const ctx = document.getElementById('expensesChart').getContext('2d');
const expensesChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode($labels); ?>,
        datasets: [{
            label: 'Expenses Over Time',
            data: <?php echo json_encode($data); ?>,
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1
        }]
    },
    options: {
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Add Expense Button Functionality
document.getElementById('add-expense-btn').addEventListener('click', function() {
    window.location.href = 'add_expense.php'; // Redirect to add expense page
});
</script>

<?php include "../footer.php" ?>
</body>
</html>